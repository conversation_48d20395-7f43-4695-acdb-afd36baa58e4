/**
 * Correction Tool
 * 
 * Provides iterative improvement and correction capabilities for translations.
 * This tool analyzes translated content and suggests improvements for:
 * - Grammar and syntax correctness
 * - Natural Polish language flow
 * - Character consistency
 * - Cultural adaptation
 * - Emotional tone preservation
 */

import Anthropic from '@anthropic-ai/sdk';

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  SUCCESS: '\x1b[32m\x1b[1m',
  ERROR: '\x1b[31m\x1b[1m',
  WARNING: '\x1b[33m\x1b[1m',
  INFO: '\x1b[36m',
  DEBUG: '\x1b[90m',
  HIGHLIGHT: '\x1b[35m\x1b[1m',
  QUALITY: '\x1b[32m',
  IMPROVEMENT: '\x1b[33m\x1b[1m'
};

export class CorrectionTool {
  constructor(options = {}) {
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });

    // Claude 4 for analysis, Claude 3.5 for generation
    this.analysisModel = "claude-sonnet-4-20250514";
    this.generationModel = "claude-3-5-sonnet-20241022";
    this.maxIterations = options.maxIterations || 4;
    this.qualityThreshold = options.qualityThreshold || 0.85;
    this.enableGrammarCheck = options.enableGrammarCheck !== false;
    this.enableConsistencyCheck = options.enableConsistencyCheck !== false;
    this.enableCulturalAdaptation = options.enableCulturalAdaptation !== false;

    console.log(`${COLORS.SUCCESS}🔧 [CorrectionTool] Initialized with dual-model approach:${COLORS.RESET}`);
    console.log(`${COLORS.INFO}   📊 Analysis Model: Claude 4 Sonnet (${this.analysisModel})${COLORS.RESET}`);
    console.log(`${COLORS.INFO}   🔧 Generation Model: Claude 3.5 Sonnet (${this.generationModel})${COLORS.RESET}`);
  }

  /**
   * Improve translation through iterative correction
   * @param {string} originalText - Original English text
   * @param {string} translatedText - Initial Polish translation
   * @param {Object} context - Translation context
   * @returns {Promise<string>} - Improved translation
   */
  async improveTranslation(originalText, translatedText, context = {}) {
    try {
      console.log(`${COLORS.IMPROVEMENT}🔄 [CorrectionTool] Starting translation improvement process...${COLORS.RESET}`);
      console.log(`${COLORS.DEBUG}📝 [CorrectionTool] Original text: "${originalText.substring(0, 100)}..."${COLORS.RESET}`);
      console.log(`${COLORS.DEBUG}🔤 [CorrectionTool] Initial translation: "${translatedText.substring(0, 100)}..."${COLORS.RESET}`);

      // STEP 1: Apply automatic punctuation fixes first (always applied)
      let currentTranslation = this.autoFixPunctuation(translatedText);
      let iteration = 0;

      while (iteration < this.maxIterations) {
        iteration++;
        console.log(`${COLORS.INFO}🔄 [CorrectionTool] Validation ${iteration}/${this.maxIterations}${COLORS.RESET}`);

        // Analyze current translation quality
        const analysis = await this.analyzeTranslationQuality(
          originalText,
          currentTranslation,
          context
        );

        console.log(`${COLORS.QUALITY}📊 [CorrectionTool] Quality analysis results:${COLORS.RESET}`);
        console.log(`${COLORS.QUALITY}  📝 Grammar: ${(analysis.grammar_score * 100).toFixed(1)}%${COLORS.RESET}`);
        console.log(`${COLORS.QUALITY}  🗣️  Naturalness: ${(analysis.naturalness_score * 100).toFixed(1)}%${COLORS.RESET}`);
        console.log(`${COLORS.QUALITY}  🎯 Accuracy: ${(analysis.accuracy_score * 100).toFixed(1)}%${COLORS.RESET}`);
        console.log(`${COLORS.QUALITY}  🔗 Consistency: ${(analysis.consistency_score * 100).toFixed(1)}%${COLORS.RESET}`);
        console.log(`${COLORS.QUALITY}  🌍 Cultural Adaptation: ${(analysis.cultural_adaptation_score * 100).toFixed(1)}%${COLORS.RESET}`);
        console.log(`${COLORS.HIGHLIGHT}  ⭐ Overall Score: ${(analysis.overallScore * 100).toFixed(1)}%${COLORS.RESET}`);
        console.log(`${COLORS.INFO}  🔍 Issues found: ${analysis.issues?.length || 0}${COLORS.RESET}`);

        if (analysis.issues && analysis.issues.length > 0) {
          this.displayValidatorIssues(analysis.issues);
        }

        // If quality is good enough, stop iterating
        if (analysis.overallScore >= this.qualityThreshold) {
          console.log(`${COLORS.SUCCESS}✅ [CorrectionTool] Quality threshold (${(this.qualityThreshold * 100).toFixed(0)}%) reached, stopping iterations${COLORS.RESET}`);
          break;
        }

        // Check if we should continue based on issue severity (be more lenient for medium/low issues)
        if (this.shouldSkipImprovement(analysis)) {
          console.log(`${COLORS.INFO}ℹ️  [CorrectionTool] Only minor issues found, skipping improvement${COLORS.RESET}`);
          break;
        }

        // Generate improvements based on analysis
        console.log(`${COLORS.INFO}🔄 [CorrectionTool] Improvement ${iteration}/${this.maxIterations}${COLORS.RESET}`);
        const improvedTranslation = await this.generateImprovements(
          originalText,
          currentTranslation,
          analysis,
          context
        );

        if (improvedTranslation && improvedTranslation !== currentTranslation) {
          currentTranslation = improvedTranslation;
          console.log(`${COLORS.SUCCESS}✨ [CorrectionTool] Translation improved${COLORS.RESET}`);
        } else {
          console.log(`${COLORS.WARNING}⏹️  [CorrectionTool] No further improvements possible${COLORS.RESET}`);
          break;
        }
      }

      return currentTranslation;

    } catch (error) {
      console.error('[CorrectionTool] Improvement failed:', error.message);
      console.error('[CorrectionTool] Error stack:', error.stack);
      return translatedText; // Return original translation if improvement fails
    }
  }

  /**
   * Analyze translation quality across multiple dimensions
   * @param {string} original - Original text
   * @param {string} translation - Translated text
   * @param {Object} context - Context information
   * @returns {Promise<Object>} - Quality analysis
   */
  async analyzeTranslationQuality(original, translation, context) {
    console.log(`${COLORS.INFO}🔍 [CorrectionTool] Starting quality analysis...${COLORS.RESET}`);

    const tools = [
      {
        name: "analyze_translation_quality",
        description: "Analyze translation quality across multiple dimensions",
        input_schema: {
          type: "object",
          properties: {
            grammar_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Grammar correctness score (0-1)"
            },
            naturalness_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Natural Polish language flow score (0-1)"
            },
            accuracy_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Meaning preservation accuracy score (0-1)"
            },
            consistency_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Character and terminology consistency score (0-1)"
            },
            cultural_adaptation_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Cultural adaptation appropriateness score (0-1)"
            },
            issues: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  type: { type: "string", enum: ["grammar", "naturalness", "accuracy", "consistency", "cultural"] },
                  description: { type: "string" },
                  severity: { type: "string", enum: ["low", "medium", "high"] },
                  line_number: { type: "number" },
                  suggestion: { type: "string" }
                },
                required: ["type", "description", "severity"]
              },
              description: "List of identified issues"
            }
          },
          required: ["grammar_score", "naturalness_score", "accuracy_score", "consistency_score", "cultural_adaptation_score", "issues"]
        }
      }
    ];

    const prompt = this.buildQualityAnalysisPrompt(original, translation, context);
    console.log(`${COLORS.INFO}📤 [CorrectionTool] Sending quality analysis request to Claude 4 Sonnet (${this.analysisModel})...${COLORS.RESET}`);

    try {
      const response = await this.anthropic.messages.create({
        model: this.analysisModel,
        max_tokens: 4096,
        temperature: 0.3,
        messages: [{ role: 'user', content: prompt }],
        tools: tools,
        tool_choice: { type: "tool", name: "analyze_translation_quality" }
      });

      console.log(`${COLORS.SUCCESS}📥 [CorrectionTool] Received quality analysis from Claude 4 Sonnet${COLORS.RESET}`);
      console.log(`${COLORS.DEBUG}📝 [CorrectionTool] Response content types: ${response.content.map(c => c.type).join(', ')}${COLORS.RESET}`);

      // Extract analysis from tool call
      const toolCall = response.content.find(c => c.type === 'tool_use');
      if (toolCall && toolCall.input) {
        console.log(`${COLORS.SUCCESS}🔧 [CorrectionTool] Found tool call with input${COLORS.RESET}`);
        console.log(`${COLORS.DEBUG}📊 [CorrectionTool] Raw tool input:${COLORS.RESET}`);
        console.log(`${COLORS.GRAY}${JSON.stringify(toolCall.input, null, 2)}${COLORS.RESET}`);

        const analysis = toolCall.input;
        analysis.overallScore = this.calculateOverallScore(analysis);
        console.log(`${COLORS.HIGHLIGHT}⭐ [CorrectionTool] Calculated overall score: ${(analysis.overallScore * 100).toFixed(1)}%${COLORS.RESET}`);

        return analysis;
      } else {
        console.error(`${COLORS.ERROR}❌ [CorrectionTool] No tool call found in response${COLORS.RESET}`);
        console.log(`${COLORS.DEBUG}📄 [CorrectionTool] Full response:${COLORS.RESET}`);
        console.log(`${COLORS.GRAY}${JSON.stringify(response.content, null, 2)}${COLORS.RESET}`);
        throw new Error('Quality analysis failed: No valid tool call found in API response');
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [CorrectionTool] API call failed: ${error.message}${COLORS.RESET}`);
      console.error(`${COLORS.DEBUG}🔍 [CorrectionTool] Error details: ${error}${COLORS.RESET}`);
      throw error; // Re-throw the error instead of using fallback
    }
  }

  /**
   * Generate improvements based on quality analysis
   * @param {string} original - Original text
   * @param {string} translation - Current translation
   * @param {Object} analysis - Quality analysis
   * @param {Object} context - Context information
   * @returns {Promise<string>} - Improved translation
   */
  async generateImprovements(original, translation, analysis, context) {
    const tools = [
      {
        name: "improve_translation",
        description: "Generate improved translation addressing identified issues",
        input_schema: {
          type: "object",
          properties: {
            improved_translation: {
              type: "string",
              description: "Improved Polish translation addressing the identified issues"
            },
            changes_made: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  line_number: { type: "number" },
                  original_line: { type: "string" },
                  improved_line: { type: "string" },
                  reason: { type: "string" }
                },
                required: ["original_line", "improved_line", "reason"]
              },
              description: "List of specific changes made"
            }
          },
          required: ["improved_translation", "changes_made"]
        }
      }
    ];

    const prompt = this.buildImprovementPrompt(original, translation, analysis, context);

    console.log(`${COLORS.INFO}📤 [CorrectionTool] Sending improvement generation request to Claude 3.5 Sonnet (${this.generationModel})...${COLORS.RESET}`);
    const response = await this.anthropic.messages.create({
      model: this.generationModel,
      max_tokens: 4096,
      temperature: 0.5,
      messages: [{ role: 'user', content: prompt }],
      tools: tools,
      tool_choice: { type: "tool", name: "improve_translation" }
    });
    console.log(`${COLORS.SUCCESS}📥 [CorrectionTool] Received improved translation from Claude 3.5 Sonnet${COLORS.RESET}`);

    // Extract improved translation from tool call
    const toolCall = response.content.find(c => c.type === 'tool_use');
    if (toolCall && toolCall.input && toolCall.input.improved_translation) {
      console.log(`${COLORS.DEBUG}🔍 [CorrectionTool] Processing improvement response:${COLORS.RESET}`);
      console.log(`${COLORS.DEBUG}📊 [CorrectionTool] Input keys: ${Object.keys(toolCall.input).join(', ')}${COLORS.RESET}`);
      console.log(`${COLORS.DEBUG}📋 [CorrectionTool] Changes made type: ${typeof toolCall.input.changes_made}${COLORS.RESET}`);

      // Safely handle changes_made - it might not be an array
      let changesCount = 0;
      let parsedChanges = null;

      // Try to parse changes_made if it's not an array
      if (!Array.isArray(toolCall.input.changes_made) && toolCall.input.changes_made) {
        console.log(`${COLORS.DEBUG}🔍 [CorrectionTool] Changes made is not an array, attempting to parse...${COLORS.RESET}`);

        if (typeof toolCall.input.changes_made === 'string') {
          try {
            // Try multiple parsing strategies
            parsedChanges = this.parseChangesString(toolCall.input.changes_made);
            if (Array.isArray(parsedChanges)) {
              console.log(`${COLORS.SUCCESS}✅ [CorrectionTool] Successfully parsed changes_made string as JSON array${COLORS.RESET}`);
              toolCall.input.changes_made = parsedChanges;
            } else if (parsedChanges && typeof parsedChanges === 'object') {
              // Single object - convert to array
              console.log(`${COLORS.SUCCESS}✅ [CorrectionTool] Successfully parsed changes_made string as JSON object, converting to array${COLORS.RESET}`);
              toolCall.input.changes_made = [parsedChanges];
            } else {
              console.log(`${COLORS.WARNING}⚠️  [CorrectionTool] Parsed changes_made is not an array or object: ${typeof parsedChanges}${COLORS.RESET}`);
            }
          } catch (error) {
            console.log(`${COLORS.DEBUG}🔍 [CorrectionTool] Failed to parse changes_made as JSON: ${error.message}${COLORS.RESET}`);
            // Try to extract changes information manually from the string
            const extractedChanges = this.extractChangesFromString(toolCall.input.changes_made);
            if (extractedChanges && extractedChanges.length > 0) {
              toolCall.input.changes_made = extractedChanges;
            }
          }
        }
      }

      if (Array.isArray(toolCall.input.changes_made)) {
        changesCount = toolCall.input.changes_made.length;
        console.log(`${COLORS.INFO}🔧 [CorrectionTool] Made ${changesCount} improvements${COLORS.RESET}`);

        // Show detailed changes if available
        if (changesCount > 0) {
          this.displayImprovements(toolCall.input.changes_made);
        }
      } else if (toolCall.input.changes_made) {
        console.log(`${COLORS.INFO}🔧 [CorrectionTool] Improvements applied (details not in expected format)${COLORS.RESET}`);
        // Try to extract useful information from the non-array format
        const changesStr = String(toolCall.input.changes_made);
        if (changesStr.length > 50) {
          console.log(`${COLORS.DEBUG}📋 [CorrectionTool] Changes summary: ${changesStr.substring(0, 200)}...${COLORS.RESET}`);
        }
      } else {
        console.log(`${COLORS.INFO}🔧 [CorrectionTool] Improvements applied (no detailed changes provided)${COLORS.RESET}`);
      }

      // Format and display the improved translation in human-readable format
      this.displayImprovedTranslation(toolCall.input.improved_translation);

      return toolCall.input.improved_translation;
    }

    console.log(`${COLORS.WARNING}⚠️  [CorrectionTool] No valid improvement found in response${COLORS.RESET}`);
    return null;
  }

  /**
   * Parse changes string using multiple strategies
   * @param {string} changesString - String containing changes information
   * @returns {Array|null} - Parsed changes array or null if parsing fails
   */
  parseChangesString(changesString) {
    // Strategy 1: Direct JSON parse
    try {
      const parsed = JSON.parse(changesString);
      if (Array.isArray(parsed)) {
        return parsed;
      } else if (parsed && typeof parsed === 'object') {
        // Single object is also valid
        return parsed;
      }
    } catch (error) {
      // Continue to next strategy
    }

    // Strategy 2: Fix common JSON formatting issues
    try {
      let jsonString = changesString;

      // Fix escaped quotes and newlines that might break JSON parsing
      jsonString = jsonString.replace(/\\"/g, '"').replace(/\\n/g, '\n');

      // Try to extract just the array part if it's wrapped in extra text
      const arrayMatch = jsonString.match(/\[[\s\S]*\]/);
      if (arrayMatch) {
        jsonString = arrayMatch[0];
      } else {
        // Try to extract object part if it's wrapped in extra text
        const objectMatch = jsonString.match(/\{[\s\S]*\}/);
        if (objectMatch) {
          jsonString = objectMatch[0];
        }
      }

      const parsed = JSON.parse(jsonString);
      if (Array.isArray(parsed)) {
        return parsed;
      } else if (parsed && typeof parsed === 'object') {
        return parsed;
      }
    } catch (error) {
      // Continue to next strategy
    }

    // Strategy 3: Try to fix malformed JSON by adding missing quotes/brackets
    try {
      let jsonString = changesString;

      // Add missing quotes around property names
      jsonString = jsonString.replace(/(\w+):/g, '"$1":');

      // Try to parse as object first
      if (jsonString.trim().startsWith('{')) {
        const parsed = JSON.parse(jsonString);
        if (parsed && typeof parsed === 'object') {
          return parsed;
        }
      }

      // If not an object, ensure the string is wrapped in array brackets
      if (!jsonString.trim().startsWith('[')) {
        jsonString = '[' + jsonString + ']';
      }

      const parsed = JSON.parse(jsonString);
      if (Array.isArray(parsed)) {
        return parsed;
      }
    } catch (error) {
      // All strategies failed
    }

    return null;
  }

  /**
   * Extract changes information from malformed string
   * @param {string} changesString - String containing changes information
   * @returns {Array} - Array of extracted changes or empty array
   */
  extractChangesFromString(changesString) {
    console.log(`${COLORS.INFO}🔧 [CorrectionTool] Attempting to extract changes from malformed string${COLORS.RESET}`);

    // Try to extract line changes using regex patterns
    const linePatterns = [
      /"original_line":\s*"([^"]+)"/g,
      /"improved_line":\s*"([^"]+)"/g,
      /"reason":\s*"([^"]+)"/g
    ];

    const originals = [];
    const improved = [];
    const reasons = [];

    let match;
    while ((match = linePatterns[0].exec(changesString)) !== null) {
      originals.push(match[1]);
    }
    while ((match = linePatterns[1].exec(changesString)) !== null) {
      improved.push(match[1]);
    }
    while ((match = linePatterns[2].exec(changesString)) !== null) {
      reasons.push(match[1]);
    }

    const extractedChanges = [];

    if (originals.length > 0 && improved.length > 0) {
      console.log(`${COLORS.SUCCESS}✅ [CorrectionTool] Extracted ${originals.length} changes from string${COLORS.RESET}`);
      console.log(`${COLORS.DEBUG}📋 [CorrectionTool] Extracted changes:${COLORS.RESET}`);

      for (let i = 0; i < Math.min(originals.length, improved.length); i++) {
        const change = {
          original_line: originals[i],
          improved_line: improved[i],
          reason: reasons[i] || 'Change applied'
        };
        extractedChanges.push(change);

        console.log(`${COLORS.DEBUG}  ${i + 1}. ${change.reason}${COLORS.RESET}`);
        console.log(`${COLORS.GRAY}     Before: "${change.original_line}"${COLORS.RESET}`);
        console.log(`${COLORS.IMPROVEMENT}     After:  "${change.improved_line}"${COLORS.RESET}`);
      }
    } else {
      console.log(`${COLORS.WARNING}⚠️  [CorrectionTool] Could not extract structured changes from string${COLORS.RESET}`);
    }

    return extractedChanges;
  }

  /**
   * Display improved translation in human-readable format
   * @param {string} translation - The improved translation
   */
  displayImprovedTranslation(translation) {
    console.log(`${COLORS.SUCCESS}📝 [CorrectionTool] Improved Translation (Human-Readable Format):${COLORS.RESET}`);
    console.log(`${COLORS.CYAN}${'='.repeat(60)}${COLORS.RESET}`);

    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(translation);

      if (Array.isArray(parsed)) {
        parsed.forEach((item, index) => {
          console.log(`${COLORS.HIGHLIGHT}${index + 1}. ${item.speaker || 'Unknown'}:${COLORS.RESET} ${item.translation || item.text || 'No translation'}`);
          if (item.original) {
            console.log(`${COLORS.GRAY}   Original: "${item.original}"${COLORS.RESET}`);
          }
          if (item.notes) {
            console.log(`${COLORS.INFO}   Notes: ${item.notes}${COLORS.RESET}`);
          }
          console.log('');
        });
      } else {
        // Single object
        console.log(`${COLORS.HIGHLIGHT}${parsed.speaker || 'Unknown'}:${COLORS.RESET} ${parsed.translation || parsed.text || 'No translation'}`);
        if (parsed.original) {
          console.log(`${COLORS.GRAY}Original: "${parsed.original}"${COLORS.RESET}`);
        }
      }
    } catch (error) {
      // Not JSON, treat as plain text
      const lines = translation.split('\n');
      lines.forEach((line, index) => {
        if (line.trim()) {
          console.log(`${COLORS.HIGHLIGHT}${index + 1}.${COLORS.RESET} ${line.trim()}`);
        }
      });
    }

    console.log(`${COLORS.CYAN}${'='.repeat(60)}${COLORS.RESET}`);
  }

  /**
   * Display improvements in human-readable format
   * @param {Array} changes - Array of changes made
   */
  displayImprovements(changes) {
    console.log(`${COLORS.SUCCESS}🔧 [CorrectionTool] Improvements Applied (Human-Readable Format):${COLORS.RESET}`);
    console.log(`${COLORS.YELLOW}${'='.repeat(60)}${COLORS.RESET}`);

    changes.forEach((change, index) => {
      if (change && typeof change === 'object') {
        console.log(`${COLORS.HIGHLIGHT}${index + 1}. ${change.reason || 'Improvement applied'}${COLORS.RESET}`);
        if (change.line_number) {
          console.log(`${COLORS.INFO}   Line ${change.line_number}${COLORS.RESET}`);
        }
        console.log(`${COLORS.GRAY}   Before: "${change.original_line || 'N/A'}"${COLORS.RESET}`);
        console.log(`${COLORS.SUCCESS}   After:  "${change.improved_line || 'N/A'}"${COLORS.RESET}`);
        console.log('');
      } else {
        console.log(`${COLORS.DEBUG}${index + 1}. ${String(change)}${COLORS.RESET}`);
      }
    });

    console.log(`${COLORS.YELLOW}${'='.repeat(60)}${COLORS.RESET}`);
  }

  /**
   * Display validator issues in human-readable format
   * @param {Array} issues - Array of issues found by the validator
   */
  displayValidatorIssues(issues) {
    console.log(`${COLORS.WARNING}⚠️  [CorrectionTool] Issues found by validator:${COLORS.RESET}`);

    // Group issues by severity
    const highIssues = issues.filter(issue => issue.severity === 'high');
    const mediumIssues = issues.filter(issue => issue.severity === 'medium');
    const lowIssues = issues.filter(issue => issue.severity === 'low');

    if (highIssues.length > 0) {
      console.log(`${COLORS.ERROR}  🔴 HIGH severity issues (${highIssues.length}):${COLORS.RESET}`);
      highIssues.forEach((issue, i) => {
        console.log(`${COLORS.ERROR}    ${i + 1}. ${issue.type}: ${issue.description}${COLORS.RESET}`);
        if (issue.line_number) {
          console.log(`${COLORS.GRAY}       Line ${issue.line_number}${COLORS.RESET}`);
        }
      });
    }

    if (mediumIssues.length > 0) {
      console.log(`${COLORS.WARNING}  🟡 MEDIUM severity issues (${mediumIssues.length}):${COLORS.RESET}`);
      mediumIssues.forEach((issue, i) => {
        console.log(`${COLORS.WARNING}    ${i + 1}. ${issue.type}: ${issue.description}${COLORS.RESET}`);
        if (issue.line_number) {
          console.log(`${COLORS.GRAY}       Line ${issue.line_number}${COLORS.RESET}`);
        }
      });
    }

    if (lowIssues.length > 0) {
      console.log(`${COLORS.INFO}  🟢 LOW severity issues (${lowIssues.length}):${COLORS.RESET}`);
      lowIssues.forEach((issue, i) => {
        console.log(`${COLORS.INFO}    ${i + 1}. ${issue.type}: ${issue.description}${COLORS.RESET}`);
        if (issue.line_number) {
          console.log(`${COLORS.GRAY}       Line ${issue.line_number}${COLORS.RESET}`);
        }
      });
    }
  }

  /**
   * Check if improvement should be skipped based on issue severity (be more lenient)
   * @param {Object} analysis - Quality analysis
   * @returns {boolean} - Whether to skip improvement
   */
  shouldSkipImprovement(analysis) {
    if (!analysis.issues || analysis.issues.length === 0) {
      return true; // No issues, skip improvement
    }

    // Count high severity issues
    const highIssues = analysis.issues.filter(issue => issue.severity === 'high');
    const mediumIssues = analysis.issues.filter(issue => issue.severity === 'medium');

    // Only proceed with improvement if there are high severity issues
    // or more than 2 medium severity issues
    if (highIssues.length === 0 && mediumIssues.length <= 2) {
      return true; // Skip improvement for minor issues
    }

    return false; // Proceed with improvement
  }

  /**
   * Build prompt for quality analysis
   * @param {string} original - Original text
   * @param {string} translation - Translation to analyze
   * @param {Object} context - Context information
   * @returns {string} - Analysis prompt
   */
  buildQualityAnalysisPrompt(original, translation, context) {
    return `Please analyze the quality of this Polish translation of anime dialogue:

ORIGINAL ENGLISH:
${original}

POLISH TRANSLATION:
${translation}

CONTEXT:
- Characters: ${context.characters?.join(', ') || 'Unknown'}
- Scene type: ${context.sceneType || 'Unknown'}
- Emotional tone: ${context.emotionalTone || 'Unknown'}
- Previous translations: ${context.previousTranslations ? 'Available' : 'None'}

Please evaluate the translation across these dimensions with HIGH STANDARDS:
1. Grammar correctness (Polish grammar rules, case declension, verb aspects) - Be strict about errors
2. Naturalness (how natural it sounds to Polish speakers) - CRITICAL: Must sound like native Polish, not translated text
   - Accept casual expressions like "Co słychać?" (natural casual greeting)
   - Accept colloquial phrases like "zgraną paczką" (typical Polish expression for friend group)
   - Focus on avoiding overly formal language that doesn't fit casual anime dialogue
3. Accuracy (preservation of original meaning and nuance) - CRITICAL: Must preserve exact meaning and emotional tone
4. Consistency (character speech patterns, terminology) - Maintain character voice consistency
5. Cultural adaptation (appropriate for Polish anime audience) - Secondary priority

SCORING GUIDELINES:
- Score 1.0 (100%) for any dimension that has NO problems whatsoever
- Use consistent severity-based scoring:
  * HIGH severity issue: -20 to -30 points (e.g., 100% → 70-80%)
  * MEDIUM severity issue: -10 to -15 points (e.g., 100% → 85-90%)
  * LOW severity issue: -5 to -10 points (e.g., 100% → 90-95%)
  * Multiple issues: Cumulative reduction but cap minimum at 30%

SPECIFIC DIMENSION GUIDELINES:
- Grammar: HIGH issues (major errors) = 60-70%, MEDIUM issues = 80-85%, LOW issues = 90-95%
- Naturalness: HIGH issues (sounds translated) = 60-70%, MEDIUM issues = 80-85%, LOW issues = 90-95%
- Accuracy: HIGH issues (meaning lost) = 50-70%, MEDIUM issues = 75-85%, LOW issues = 85-95%
- Consistency: Score 1.0 if no issues, otherwise reduce proportionally based on severity
- Cultural adaptation: Score 1.0 if perfectly adapted, otherwise reduce proportionally

Be consistent with severity-to-score mapping across all dimensions.

SCORING EXAMPLES:
- 1 HIGH grammar issue → Grammar score: 65-75%
- 1 MEDIUM + 1 LOW accuracy issue → Accuracy score: 80-85% (not exactly 80%)
- No consistency issues → Consistency score: 100%
- 2 MEDIUM naturalness issues → Naturalness score: 70-80%

Identify specific issues and provide scores for each dimension that logically reflect the severity and number of problems found.`;
  }

  /**
   * Build prompt for generating improvements
   * @param {string} original - Original text
   * @param {string} translation - Current translation
   * @param {Object} analysis - Quality analysis
   * @param {Object} context - Context information
   * @returns {string} - Improvement prompt
   */
  buildImprovementPrompt(original, translation, analysis, context) {
    const issuesSummary = analysis.issues
      .map(issue => `- ${issue.type}: ${issue.description} (${issue.severity})`)
      .join('\n');

    return `Please improve this Polish translation by addressing the identified issues:

ORIGINAL ENGLISH:
${original}

CURRENT POLISH TRANSLATION:
${translation}

IDENTIFIED ISSUES:
${issuesSummary}

QUALITY SCORES:
- Grammar: ${analysis.grammar_score}
- Naturalness: ${analysis.naturalness_score}
- Accuracy: ${analysis.accuracy_score}
- Consistency: ${analysis.consistency_score}
- Cultural adaptation: ${analysis.cultural_adaptation_score}

CONTEXT:
- Characters: ${context.characters?.join(', ') || 'Unknown'}
- Scene type: ${context.sceneType || 'Unknown'}
- Emotional tone: ${context.emotionalTone || 'Unknown'}

IMPORTANT GUIDELINES:
1. **Honorifics**: If there are suffixes or honorifics (like -sama, -chan, -kun), leave them as they are and do not delete or inflect them
2. **Monologues/Internal thoughts**: Take special notice of monologues or internal speech (thoughts) that might be about other persons, especially with different gender, or "what if" scenarios that play in the head of a character when they speak in the voice of another character
3. **Second Verifier**: Do not always use the Second Verifier - only use it after initial translation to clear up ambiguities or odd phrasing and other issues. The AI model should decide when to use it or not.

Please provide an improved translation that:
1. Maintains the exact number of lines
2. Preserves speaker information and formatting
3. Addresses the identified issues
4. Sounds natural in Polish
5. Maintains character consistency
6. Preserves honorifics unchanged
7. Handles character voice changes in internal thoughts appropriately

Focus on the most critical issues first, especially those marked as "high" severity.`;
  }

  /**
   * Calculate overall quality score from individual scores
   * @param {Object} analysis - Quality analysis
   * @returns {number} - Overall score (0-1)
   */
  calculateOverallScore(analysis) {
    const weights = {
      grammar_score: 0.20,
      naturalness_score: 0.35,  // Increased weight for naturalness
      accuracy_score: 0.35,     // Increased weight for accuracy
      consistency_score: 0.10,  // Reduced weight
      cultural_adaptation_score: 0.00  // Removed weight to focus on core quality
    };

    let weightedSum = 0;
    let totalWeight = 0;

    for (const [key, weight] of Object.entries(weights)) {
      if (typeof analysis[key] === 'number') {
        weightedSum += analysis[key] * weight;
        totalWeight += weight;
      }
    }

    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }

  /**
   * Check specific grammar rules for Polish
   * @param {string} text - Text to check
   * @returns {Array} - Array of grammar issues
   */
  checkPolishGrammar(text) {
    const issues = [];
    const lines = text.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Skip speaker labels (everything before the first colon)
      const colonIndex = line.indexOf(':');
      if (colonIndex === -1) continue;
      const dialogue = line.slice(colonIndex + 1).trim();
      if (!dialogue) continue;

      // Check for common Polish grammar issues
      if (this.enableGrammarCheck) {

        // === PUNCTUATION ERRORS (HIGH PRIORITY - ALWAYS FIX) ===

        // Missing comma before "że"
        if (/\s+że\s+/.test(dialogue) && !/,\s*że/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "że"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "że": "Myślę, że..."',
            autofix: true
          });
        }

        // Missing comma before "żeby"
        if (/\s+żeby\s+/.test(dialogue) && !/,\s*żeby/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "żeby"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "żeby": "Chcę, żeby..."',
            autofix: true
          });
        }

        // Missing comma before "aby"
        if (/\s+aby\s+/.test(dialogue) && !/,\s*aby/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "aby"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "aby": "Proszę, aby..."',
            autofix: true
          });
        }

        // Missing comma before "bo"
        if (/\s+bo\s+/.test(dialogue) && !/,\s*bo/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "bo"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "bo": "Nie mogę, bo..."',
            autofix: true
          });
        }

        // Missing comma before "ale"
        if (/\s+ale\s+/.test(dialogue) && !/,\s*ale/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "ale"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "ale": "Tak, ale..."',
            autofix: true
          });
        }

        // Missing comma before "jednak"
        if (/\s+jednak\s+/.test(dialogue) && !/,\s*jednak/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "jednak"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "jednak": "Myślałem, jednak..."',
            autofix: true
          });
        }

        // Missing comma before "więc"
        if (/\s+więc\s+/.test(dialogue) && !/,\s*więc/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "więc"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "więc": "Tak, więc..."',
            autofix: true
          });
        }

        // Missing comma before "dlatego"
        if (/\s+dlatego\s+/.test(dialogue) && !/,\s*dlatego/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "dlatego"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "dlatego": "Spóźnił się, dlatego..."',
            autofix: true
          });
        }

        // Missing comma before "ponieważ"
        if (/\s+ponieważ\s+/.test(dialogue) && !/,\s*ponieważ/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "ponieważ"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "ponieważ": "Zostałem, ponieważ..."',
            autofix: true
          });
        }

        // Missing comma before "chociaż"
        if (/\s+chociaż\s+/.test(dialogue) && !/,\s*chociaż/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Missing comma before "chociaż"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Add comma before "chociaż": "Poszedł, chociaż..."',
            autofix: true
          });
        }

        // Comma before "i" (incorrect)
        if (/,\s*i\s+/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Unnecessary comma before "i"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Remove comma before "i": "Poszedł i wrócił"',
            autofix: true
          });
        }

        // Comma before "oraz" (incorrect)
        if (/,\s*oraz\s+/.test(dialogue)) {
          issues.push({
            type: 'punctuation',
            description: 'Unnecessary comma before "oraz"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Remove comma before "oraz": "Książki oraz zeszyty"',
            autofix: true
          });
        }

        // === HONORIFIC ERRORS ===

        // Check for incorrect honorific inflection
        if (/-sam[aąęiej]/.test(dialogue) && !/-sama$/.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Japanese honorific incorrectly inflected',
            severity: 'medium',
            line_number: i + 1,
            suggestion: 'Keep honorifics like -sama, -chan, -kun uninflected'
          });
        }

        // Check for inflected -chan, -kun
        if (/-chan[aąęiej]/.test(dialogue) || /-kun[aąęiej]/.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Japanese honorific incorrectly inflected',
            severity: 'medium',
            line_number: i + 1,
            suggestion: 'Keep honorifics like -chan, -kun uninflected'
          });
        }

        // === COMMON POLISH GRAMMAR ERRORS ===

        // Incorrect use of "na" vs "w" with time expressions
        if (/na\s+(rano|wieczór|noc)/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Incorrect preposition with time expression',
            severity: 'medium',
            line_number: i + 1,
            suggestion: 'Use "w" instead of "na": "w nocy", "wieczorem"'
          });
        }

        // Incorrect "czy" usage (double question)
        if (/czy.*\?.*czy/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Redundant "czy" in question',
            severity: 'medium',
            line_number: i + 1,
            suggestion: 'Use only one "czy" in questions'
          });
        }

        // Missing "się" with reflexive verbs
        const reflexiveVerbs = ['myć', 'uczyć', 'bać', 'śmiać', 'martwić', 'cieszyć', 'denerwować'];
        reflexiveVerbs.forEach(verb => {
          const pattern = new RegExp(`\\b${verb}\\b(?!.*się)`, 'i');
          if (pattern.test(dialogue)) {
            issues.push({
              type: 'grammar',
              description: `Missing "się" with reflexive verb "${verb}"`,
              severity: 'medium',
              line_number: i + 1,
              suggestion: `Add "się": "${verb} się"`
            });
          }
        });

        // Incorrect case after numbers
        if (/\b([2-4])\s+([a-ząćęłńóśźż]+y)\b/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Incorrect case after numbers 2-4',
            severity: 'medium',
            line_number: i + 1,
            suggestion: 'Use nominative plural after 2-4: "2 koty", "3 domy"'
          });
        }

        // Incorrect "który" vs "co" usage
        if (/co\s+(jest|było|będzie|ma|miał)/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Consider using "który" instead of "co"',
            severity: 'low',
            line_number: i + 1,
            suggestion: 'Use "który" for relative clauses: "dom, który..."'
          });
        }

        // === WORD ORDER ISSUES ===

        // Auxiliary verb placement
        if (/\b(będę|będziesz|będzie|będziemy|będziecie|będą)\s+\w+\s+(robić|mówić|iść|jechać)/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Unusual word order with future tense',
            severity: 'low',
            line_number: i + 1,
            suggestion: 'Consider: "będę robić" or "robić będę" (emphatic)'
          });
        }

        // === CAPITALIZATION ERRORS ===

        // Months and days should be lowercase
        const monthsAndDays = ['styczeń', 'luty', 'marzec', 'kwiecień', 'maj', 'czerwiec',
                              'lipiec', 'sierpień', 'wrzesień', 'październik', 'listopad', 'grudzień',
                              'poniedziałek', 'wtorek', 'środa', 'czwartek', 'piątek', 'sobota', 'niedziela'];

        monthsAndDays.forEach(word => {
          const capitalizedPattern = new RegExp(`\\b${word.charAt(0).toUpperCase()}${word.slice(1)}\\b`);
          if (capitalizedPattern.test(dialogue)) {
            issues.push({
              type: 'grammar',
              description: `Month/day names should be lowercase: "${word}"`,
              severity: 'low',
              line_number: i + 1,
              suggestion: `Use lowercase: "${word}"`
            });
          }
        });

        // === COMMON SPELLING/USAGE ERRORS ===

        // "Wogóle" instead of "w ogóle"
        if (/wogóle/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Incorrect spelling of "w ogóle"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Write separately: "w ogóle"'
          });
        }

        // "Narazie" instead of "na razie"
        if (/narazie/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Incorrect spelling of "na razie"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Write separately: "na razie"'
          });
        }

        // "Niemożliwe" vs "nie możliwe"
        if (/nie\s+możliwe/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Consider writing together: "niemożliwe"',
            severity: 'low',
            line_number: i + 1,
            suggestion: 'Usually written together: "niemożliwe"'
          });
        }

        // === ANGLICISMS AND CALQUES ===

        // "Robić sens" (English calque)
        if (/robić\s+sens/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'English calque "robić sens"',
            severity: 'high',
            line_number: i + 1,
            suggestion: 'Use "mieć sens" instead of "robić sens"'
          });
        }

        // "Czuć się komfortowo" (English calque)
        if (/czuć\s+się\s+komfortowo/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'English calque "czuć się komfortowo"',
            severity: 'medium',
            line_number: i + 1,
            suggestion: 'Use "czuć się swobodnie" or "czuć się wygodnie"'
          });
        }

        // === REDUNDANT WORDS ===

        // "Bardzo dużo" (redundant)
        if (/bardzo\s+dużo/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Redundant expression "bardzo dużo"',
            severity: 'low',
            line_number: i + 1,
            suggestion: 'Use either "bardzo" or "dużo", not both'
          });
        }

        // "Całkowicie kompletnie" (redundant)
        if (/całkowicie\s+kompletnie|kompletnie\s+całkowicie/i.test(dialogue)) {
          issues.push({
            type: 'grammar',
            description: 'Redundant expression with synonymous adverbs',
            severity: 'medium',
            line_number: i + 1,
            suggestion: 'Use either "całkowicie" or "kompletnie", not both'
          });
        }
      }
    }

    return issues;
  }

  /**
   * Automatically fix punctuation errors in Polish text
   * @param {string} text - Text to fix
   * @returns {string} - Text with punctuation fixes applied
   */
  autoFixPunctuation(text) {
    console.log(`${COLORS.INFO}🔧 [CorrectionTool] Applying automatic punctuation fixes...${COLORS.RESET}`);

    let fixedText = text;
    let fixCount = 0;

    // Fix missing commas before conjunctions (high priority)
    const conjunctions = ['że', 'żeby', 'aby', 'bo', 'ale', 'jednak', 'więc', 'dlatego', 'ponieważ', 'chociaż'];

    conjunctions.forEach(conjunction => {
      const pattern = new RegExp(`(\\w)\\s+${conjunction}\\s+`, 'g');
      const replacement = `$1, ${conjunction} `;
      const beforeFix = fixedText;
      fixedText = fixedText.replace(pattern, replacement);

      if (beforeFix !== fixedText) {
        const matches = (beforeFix.match(pattern) || []).length;
        fixCount += matches;
        console.log(`${COLORS.SUCCESS}✅ [CorrectionTool] Fixed ${matches} missing comma(s) before "${conjunction}"${COLORS.RESET}`);
      }
    });

    // Remove incorrect commas before coordinating conjunctions
    const coordinatingConjunctions = ['i', 'oraz', 'a'];

    coordinatingConjunctions.forEach(conjunction => {
      const pattern = new RegExp(`,\\s*${conjunction}\\s+`, 'g');
      const replacement = ` ${conjunction} `;
      const beforeFix = fixedText;
      fixedText = fixedText.replace(pattern, replacement);

      if (beforeFix !== fixedText) {
        const matches = (beforeFix.match(pattern) || []).length;
        fixCount += matches;
        console.log(`${COLORS.SUCCESS}✅ [CorrectionTool] Removed ${matches} incorrect comma(s) before "${conjunction}"${COLORS.RESET}`);
      }
    });

    // Fix double spaces that might result from comma fixes
    fixedText = fixedText.replace(/\s{2,}/g, ' ');

    if (fixCount > 0) {
      console.log(`${COLORS.HIGHLIGHT}🎉 [CorrectionTool] Applied ${fixCount} automatic punctuation fixes${COLORS.RESET}`);
    } else {
      console.log(`${COLORS.DEBUG}✨ [CorrectionTool] No punctuation fixes needed${COLORS.RESET}`);
    }

    return fixedText;
  }

  /**
   * Check translation consistency
   * @param {string} text - Text to check
   * @param {Object} context - Context with previous translations
   * @returns {Array} - Array of consistency issues
   */
  checkConsistency(text, context) {
    const issues = [];

    if (this.enableConsistencyCheck && context.characterTerminology) {
      // Check for consistent character name translations
      // This would be expanded with actual consistency checking logic
      // For now, we'll just use the text parameter to avoid the warning
      if (text && text.length > 0) {
        // Placeholder for future consistency checking logic
      }
    }

    return issues;
  }
}
